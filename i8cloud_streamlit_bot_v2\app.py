import streamlit as st
import os
from dotenv import load_dotenv
import requests
from prompt_templates import linkedin_post_prompt, image_prompt
from news_utils import fetch_latest_news

load_dotenv()
HUGGINGFACE_API_TOKEN = os.getenv("HUGGINGFACE_API_TOKEN")
ZAPIER_WEBHOOK_URL = os.getenv("ZAPIER_WEBHOOK_URL")

headers = {"Authorization": f"Bearer {HUGGINGFACE_API_TOKEN}"}

# --- TEXT GENERATION (using Falcon model or similar) ---
def get_gpt_response(prompt):
    api_url = "https://api-inference.huggingface.co/models/tiiuae/falcon-7b-instruct"
    response = requests.post(api_url, headers=headers, json={"inputs": prompt})
    if response.status_code == 200:
        return response.json()[0]["generated_text"].strip()
    else:
        return "Error generating text."

# --- IMAGE GENERATION (using Stable Diffusion) ---
def generate_dalle_image(topic):
    prompt = image_prompt(topic)
    api_url = "https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-2"
    response = requests.post(api_url, headers=headers, json={"inputs": prompt})
    if response.status_code == 200:
        image_bytes = response.content
        with open("generated_image.png", "wb") as f:
            f.write(image_bytes)
        return "generated_image.png"
    else:
        return None

def send_to_zapier(payload):
    if not ZAPIER_WEBHOOK_URL:
        return "Zapier webhook not configured."
    response = requests.post(ZAPIER_WEBHOOK_URL, json=payload)
    return response.status_code

# --- STREAMLIT UI ---
st.title("📢 i8CLOUD Auto Content Bot")

topic_choice = st.selectbox("Select Topic", ["Workday", "Odoo"])
tone = st.selectbox("Select Tone", ["Professional", "Informative", "Conversational"])

if st.button("Generate Post"):
    with st.spinner("Fetching latest news..."):
        news = fetch_latest_news(topic_choice)

    if news:
        title = news.title
        link = news.link
        summary = news.summary

        st.subheader("📰 Latest News")
        st.markdown(f"**{title}**\n\n{summary}\n\n[Read more]({link})")

        prompt = linkedin_post_prompt(title, tone)
        post_text = get_gpt_response(prompt)
        st.subheader("✍️ Generated Post")
        st.text_area("Post Content", post_text, height=200)

        image_path = generate_dalle_image(title)
        st.subheader("🖼️ Suggested Image")
        if image_path:
            st.image(image_path, width=500)
        else:
            st.error("Image generation failed.")

        if st.button("🚀 Send to Zapier"):
            zap_payload = {
                "topic": topic_choice,
                "title": title,
                "summary": summary,
                "post": post_text,
                "image_url": image_path,
                "link": link
            }
            status = send_to_zapier(zap_payload)
            if status == 200:
                st.success("Successfully sent to Zapier!")
            else:
                st.error("Failed to send to Zapier.")
    else:
        st.error("No news found for the selected topic.")
