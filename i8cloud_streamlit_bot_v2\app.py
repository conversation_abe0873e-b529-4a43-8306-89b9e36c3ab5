import streamlit as st
import os
from dotenv import load_dotenv
import requests
import json
import time
from typing import Optional, Dict, Any
from prompt_templates import get_system_prompt, get_user_prompt, image_prompt
from news_utils import fetch_latest_news

load_dotenv()

# Configuration - Support multiple providers
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
TOGETHER_API_KEY = os.getenv("TOGETHER_API_KEY")
OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
ZAPIER_WEBHOOK_URL = os.getenv("ZAPIER_WEBHOOK_URL")

# Model configurations
MODELS = {
    "groq": {
        "api_url": "https://api.groq.com/openai/v1/chat/completions",
        "model": "llama-3.1-8b-instant",  # Fast and good quality
        "headers": {"Authorization": f"Bearer {GROQ_API_KEY}", "Content-Type": "application/json"}
    },
    "together": {
        "api_url": "https://api.together.xyz/v1/chat/completions",
        "model": "meta-llama/Llama-3-8b-chat-hf",
        "headers": {"Authorization": f"Bearer {TOGETHER_API_KEY}", "Content-Type": "application/json"}
    },
    "ollama": {
        "api_url": f"{OLLAMA_BASE_URL}/api/chat",
        "model": "llama3.1:8b",  # or "mistral:7b"
        "headers": {"Content-Type": "application/json"}
    }
}

def generate_content_with_system_prompt(topic: str, tone: str, news_context: str, provider: str = "groq") -> Optional[str]:
    """
    Generate LinkedIn post using open-source models with proper system prompts
    """
    try:
        if provider not in MODELS:
            st.error(f"Provider {provider} not configured")
            return None

        config = MODELS[provider]
        system_prompt = get_system_prompt()
        user_prompt = get_user_prompt(topic, tone, news_context)

        if provider == "ollama":
            # Ollama uses different format
            payload = {
                "model": config["model"],
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "stream": False
            }
        else:
            # Groq/Together use OpenAI-compatible format
            payload = {
                "model": config["model"],
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "max_tokens": 500,
                "temperature": 0.7
            }

        response = requests.post(
            config["api_url"],
            headers=config["headers"],
            json=payload,
            timeout=30
        )

        if response.status_code == 200:
            data = response.json()
            if provider == "ollama":
                return data["message"]["content"].strip()
            else:
                return data["choices"][0]["message"]["content"].strip()
        else:
            st.error(f"API Error ({response.status_code}): {response.text}")
            return None

    except requests.exceptions.Timeout:
        st.error("Request timed out. Please try again.")
        return None
    except requests.exceptions.RequestException as e:
        st.error(f"Network error: {str(e)}")
        return None
    except Exception as e:
        st.error(f"Unexpected error: {str(e)}")
        return None

# --- IMAGE GENERATION (using Stable Diffusion) ---
def generate_dalle_image(topic):
    prompt = image_prompt(topic)
    api_url = "https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-2"
    response = requests.post(api_url, headers=headers, json={"inputs": prompt})
    if response.status_code == 200:
        image_bytes = response.content
        with open("generated_image.png", "wb") as f:
            f.write(image_bytes)
        return "generated_image.png"
    else:
        return None

def send_to_zapier(payload):
    if not ZAPIER_WEBHOOK_URL:
        return "Zapier webhook not configured."
    response = requests.post(ZAPIER_WEBHOOK_URL, json=payload)
    return response.status_code

# --- STREAMLIT UI ---
st.title("📢 i8CLOUD Auto Content Bot")

topic_choice = st.selectbox("Select Topic", ["Workday", "Odoo"])
tone = st.selectbox("Select Tone", ["Professional", "Informative", "Conversational"])

if st.button("Generate Post"):
    with st.spinner("Fetching latest news..."):
        news = fetch_latest_news(topic_choice)

    if news:
        title = news.title
        link = news.link
        summary = news.summary

        st.subheader("📰 Latest News")
        st.markdown(f"**{title}**\n\n{summary}\n\n[Read more]({link})")

        prompt = linkedin_post_prompt(title, tone)
        post_text = get_gpt_response(prompt)
        st.subheader("✍️ Generated Post")
        st.text_area("Post Content", post_text, height=200)

        image_path = generate_dalle_image(title)
        st.subheader("🖼️ Suggested Image")
        if image_path:
            st.image(image_path, width=500)
        else:
            st.error("Image generation failed.")

        if st.button("🚀 Send to Zapier"):
            zap_payload = {
                "topic": topic_choice,
                "title": title,
                "summary": summary,
                "post": post_text,
                "image_url": image_path,
                "link": link
            }
            status = send_to_zapier(zap_payload)
            if status == 200:
                st.success("Successfully sent to Zapier!")
            else:
                st.error("Failed to send to Zapier.")
    else:
        st.error("No news found for the selected topic.")
